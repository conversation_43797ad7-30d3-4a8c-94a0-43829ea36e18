
# word_counter_before.py

def count_words_in_text(text):
    """
    计算一段文本中每个单词出现的次数。
    这是一个功能正确但略显冗长的实现。
    """
    # 将文本转为小写并按空格分割成单词列表
    words = text.lower().split()
    word_counts = {}  # 初始化一个空字典

    for word in words:
        # 简单移除单词首尾的标点符号
        cleaned_word = word.strip('.,!?')
        if cleaned_word:  # 确保清理后不是空字符串
            if cleaned_word in word_counts:
                # 如果单词已在字典中，则计数加一
                word_counts[cleaned_word] = word_counts[cleaned_word] + 1
            else:
                # 如果是新单词，则添加到字典并设置计数为1
                word_counts[cleaned_word] = 1
    return word_counts

# --- 使用示例 ---
if __name__ == "__main__":
    sample_text = "Hello world! This is a test. Hello again, world."
    counts = count_words_in_text(sample_text)
    print("单词计数结果 (重构前):")
    for word, count in sorted(counts.items()):
        print(f"- {word}: {count}")
