
def calculate_factorial(n):
    """
    通过递归计算一个非负整数的阶乘。

    参数:
    n (int): 一个非负整数。

    返回:
    int: n的阶乘。
    """
    # 基本情况：如果n是0或1，阶乘就是1。
    if n == 0 or n == 1:
        return 1
    # 递归步骤：n的阶乘是 n 乘以 (n-1)的阶乘。
    else:
        return n * calculate_factorial(n - 1)

# --- 使用示例 ---
if __name__ == "__main__":
    number = 5
    result = calculate_factorial(number)
    print(f"{number} 的阶乘是: {result}")

    # 尝试一个边界情况
    number_zero = 0
    result_zero = calculate_factorial(number_zero)
    print(f"{number_zero} 的阶乘是: {result_zero}")
