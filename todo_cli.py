
import sys
import os

TASKS_FILE = "tasks.txt"

def load_tasks():
    """从文件中加载任务，如果文件不存在则返回空列表"""
    if not os.path.exists(TASKS_FILE):
        return []
    with open(TASKS_FILE, "r", encoding="utf-8") as f:
        tasks = [line.strip() for line in f.readlines()]
    return tasks

def save_tasks(tasks):
    """将任务列表保存到文件"""
    with open(TASKS_FILE, "w", encoding="utf-8") as f:
        for task in tasks:
            f.write(task + "\n")

def add_task(task_description):
    """添加一个新任务"""
    tasks = load_tasks()
    tasks.append(task_description)
    save_tasks(tasks)
    print(f"已添加任务: '{task_description}'")

def list_tasks():
    """列出所有任务"""
    tasks = load_tasks()
    if not tasks:
        print("待办事项列表为空。")
        return
    print("\n--- To-Do List ---")
    for i, task in enumerate(tasks, 1):
        print(f"{i}. {task}")
    print("--------------------
")

def remove_task(task_number):
    """根据任务编号删除一个任务"""
    tasks = load_tasks()
    try:
        task_index = int(task_number) - 1
        if 0 <= task_index < len(tasks):
            removed_task = tasks.pop(task_index)
            save_tasks(tasks)
            print(f"已删除任务: '{removed_task}'")
        else:
            print(f"错误: 任务编号 '{task_number}' 无效。")
    except ValueError:
        print(f"错误: 请输入一个有效的任务编号。")

def print_usage():
    """打印使用说明"""
    usage_text = """
用法: python todo_cli.py [命令] [参数]

命令:
  add    <"任务描述">   - 添加一个新任务
  list                  - 列出所有任务
  remove <任务编号>     - 删除一个指定的任务
  help                  - 显示此帮助信息
"""
    print(usage_text)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print_usage()
        sys.exit(1)

    command = sys.argv[1].lower()

    if command == "add":
        if len(sys.argv) < 3:
            print("错误: 'add' 命令需要任务描述。")
        else:
            add_task(" ".join(sys.argv[2:]))
    elif command == "list":
        list_tasks()
    elif command == "remove":
        if len(sys.argv) < 3:
            print("错误: 'remove' 命令需要任务编号。")
        else:
            remove_task(sys.argv[2])
    elif command == "help":
        print_usage()
    else:
        print(f"错误: 未知命令 '{command}'")
        print_usage()
